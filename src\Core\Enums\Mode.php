<?php

declare(strict_types=1);

/*
 * QR Code generator library (PHP)
 * 
 * Copyright (c) Project Nayuki. (MIT License)
 * https://www.nayuki.io/page/qr-code-generator-library
 * 
 * Permission is hereby granted, free of charge, to any person obtaining a copy of
 * this software and associated documentation files (the "Software"), to deal in
 * the Software without restriction, including without limitation the rights to
 * use, copy, modify, merge, publish, distribute, sublicense, and/or sell copies of
 * the Software, and to permit persons to whom the Software is furnished to do so,
 * subject to the following conditions:
 * - The above copyright notice and this permission notice shall be included in
 *   all copies or substantial portions of the Software.
 * - The Software is provided "as is", without warranty of any kind, express or
 *   implied, including but not limited to the warranties of merchantability,
 *   fitness for a particular purpose and noninfringement. In no event shall the
 *   authors or copyright holders be liable for any claim, damages or other
 *   liability, whether in an action of contract, tort or otherwise, arising from,
 *   out of or in connection with the Software or the use or other dealings in the
 *   Software.
 */

namespace QrCodeGen\Core\Enums;

/**
 * Describes how a segment's data bits are interpreted.
 * 
 * This enum represents the different encoding modes available in QR Code segments.
 * Each mode is optimized for different types of data to minimize the encoded size.
 */
enum Mode: int
{
    /**
     * Numeric mode - for encoding decimal digits 0-9
     */
    case NUMERIC = 0x1;
    
    /**
     * Alphanumeric mode - for encoding digits, uppercase letters, and some symbols
     */
    case ALPHANUMERIC = 0x2;
    
    /**
     * Byte mode - for encoding arbitrary binary data
     */
    case BYTE = 0x4;
    
    /**
     * Kanji mode - for encoding Japanese Kanji characters (not implemented in this port)
     */
    case KANJI = 0x8;
    
    /**
     * ECI mode - Extended Channel Interpretation for character set switching
     */
    case ECI = 0x7;

    /**
     * Returns the mode indicator bits for this mode.
     * 
     * @return int The mode bits (4-bit value)
     */
    public function getModeBits(): int
    {
        return $this->value;
    }

    /**
     * Returns the bit width of the character count field for a segment in this mode
     * in a QR Code at the given version number.
     * 
     * @param int $version The QR Code version (1-40)
     * @return int The number of bits for the character count field (0-16)
     */
    public function getNumCharCountBits(int $version): int
    {
        $numBitsCharCount = match ($this) {
            self::NUMERIC => [10, 12, 14],
            self::ALPHANUMERIC => [9, 11, 13],
            self::BYTE => [8, 16, 16],
            self::KANJI => [8, 10, 12],
            self::ECI => [0, 0, 0],
        };
        
        return $numBitsCharCount[intval(($version + 7) / 17)];
    }
}
