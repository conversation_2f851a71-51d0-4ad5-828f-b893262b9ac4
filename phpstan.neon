parameters:
    level: 8
    paths:
        - src
        - tests
    
    excludePaths:
        - tests/Fixtures/*
    
    checkMissingIterableValueType: false
    checkGenericClassInNonGenericObjectType: false
    
    ignoreErrors:
        # Allow dynamic properties in test fixtures
        - '#Access to an undefined property#'
        
    tmpDir: .phpstan.cache
    
    phpVersion: 80200
    
    strictRules:
        allRules: false
        booleansInConditions: true
        uselessCast: true
        requireParentConstructorCall: true
        disallowedLooseComparison: true
        noVariableVariables: true
        strictCalls: true
    
    reportUnmatchedIgnoredErrors: false
