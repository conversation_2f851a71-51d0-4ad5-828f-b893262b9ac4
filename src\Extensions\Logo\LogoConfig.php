<?php

declare(strict_types=1);

/*
 * QR Code generator library (PHP)
 * 
 * Copyright (c) Project Nayuki. (MIT License)
 * https://www.nayuki.io/page/qr-code-generator-library
 * 
 * Permission is hereby granted, free of charge, to any person obtaining a copy of
 * this software and associated documentation files (the "Software"), to deal in
 * the Software without restriction, including without limitation the rights to
 * use, copy, modify, merge, publish, distribute, sublicense, and/or sell copies of
 * the Software, and to permit persons to whom the Software is furnished to do so,
 * subject to the following conditions:
 * - The above copyright notice and this permission notice shall be included in
 *   all copies or substantial portions of the Software.
 * - The Software is provided "as is", without warranty of any kind, express or
 *   implied, including but not limited to the warranties of merchantability,
 *   fitness for a particular purpose and noninfringement. In no event shall the
 *   authors or copyright holders be liable for any claim, damages or other
 *   liability, whether in an action of contract, tort or otherwise, arising from,
 *   out of or in connection with the Software or the use or other dealings in the
 *   Software.
 */

namespace QrCodeGen\Extensions\Logo;

use QrCodeGen\Core\Enums\Ecc;
use QrCodeGen\Extensions\Logo\Exceptions\LogoException;

/**
 * Configuration class for QR code logo embedding.
 * 
 * This class manages all configuration options for embedding logos in QR codes,
 * including logo source, sizing, positioning, and background options.
 * 
 * @package QrCodeGen
 * <AUTHOR> Code Generator PHP Port
 * @since 1.1.0
 */
class LogoConfig
{
    /**
     * Maximum allowed logo size percentages by error correction level.
     * These values ensure QR code readability while maximizing logo size.
     */
    private const MAX_LOGO_SIZE_BY_ECC = [
        'LOW' => 0.15,      // 15% for ~7% error recovery
        'MEDIUM' => 0.20,   // 20% for ~15% error recovery  
        'QUARTILE' => 0.25, // 25% for ~25% error recovery
        'HIGH' => 0.30,     // 30% for ~30% error recovery
    ];

    /**
     * Default logo size as percentage of QR code size.
     */
    private const DEFAULT_SIZE_PERCENTAGE = 0.20;

    /**
     * Minimum allowed logo size percentage.
     */
    private const MIN_SIZE_PERCENTAGE = 0.05;

    /**
     * Default background padding in modules.
     */
    private const DEFAULT_BACKGROUND_PADDING = 2;

    private string $logoSource;
    private float $sizePercentage;
    private bool $addBackground;
    private string $backgroundColor;
    private int $backgroundPadding;
    private float $offsetX;
    private float $offsetY;
    private bool $maintainAspectRatio;

    /**
     * Creates a new logo configuration.
     *
     * @param string $logoSource Logo source (file path, URL, or SVG string)
     * @param float $sizePercentage Logo size as percentage of QR code (0.05-0.30)
     * @param bool $addBackground Whether to add background behind logo
     * @param string $backgroundColor Background color (CSS color format)
     * @param int $backgroundPadding Background padding in modules
     * @param float $offsetX Horizontal offset from center (-1.0 to 1.0)
     * @param float $offsetY Vertical offset from center (-1.0 to 1.0)
     * @param bool $maintainAspectRatio Whether to maintain logo aspect ratio
     * 
     * @throws LogoException If configuration parameters are invalid
     */
    public function __construct(
        string $logoSource,
        float $sizePercentage = self::DEFAULT_SIZE_PERCENTAGE,
        bool $addBackground = true,
        string $backgroundColor = '#FFFFFF',
        int $backgroundPadding = self::DEFAULT_BACKGROUND_PADDING,
        float $offsetX = 0.0,
        float $offsetY = 0.0,
        bool $maintainAspectRatio = true
    ) {
        $this->validateLogoSource($logoSource);
        $this->validateSizePercentage($sizePercentage);
        $this->validateBackgroundColor($backgroundColor);
        $this->validateBackgroundPadding($backgroundPadding);
        $this->validateOffset($offsetX, 'offsetX');
        $this->validateOffset($offsetY, 'offsetY');

        $this->logoSource = $logoSource;
        $this->sizePercentage = $sizePercentage;
        $this->addBackground = $addBackground;
        $this->backgroundColor = $backgroundColor;
        $this->backgroundPadding = $backgroundPadding;
        $this->offsetX = $offsetX;
        $this->offsetY = $offsetY;
        $this->maintainAspectRatio = $maintainAspectRatio;
    }

    /**
     * Gets the logo source.
     *
     * @return string The logo source (file path, URL, or SVG string)
     */
    public function getLogoSource(): string
    {
        return $this->logoSource;
    }

    /**
     * Gets the logo size percentage.
     *
     * @return float Logo size as percentage of QR code size
     */
    public function getSizePercentage(): float
    {
        return $this->sizePercentage;
    }

    /**
     * Checks if background should be added.
     *
     * @return bool True if background should be added
     */
    public function shouldAddBackground(): bool
    {
        return $this->addBackground;
    }

    /**
     * Gets the background color.
     *
     * @return string Background color in CSS format
     */
    public function getBackgroundColor(): string
    {
        return $this->backgroundColor;
    }

    /**
     * Gets the background padding.
     *
     * @return int Background padding in modules
     */
    public function getBackgroundPadding(): int
    {
        return $this->backgroundPadding;
    }

    /**
     * Gets the horizontal offset.
     *
     * @return float Horizontal offset from center (-1.0 to 1.0)
     */
    public function getOffsetX(): float
    {
        return $this->offsetX;
    }

    /**
     * Gets the vertical offset.
     *
     * @return float Vertical offset from center (-1.0 to 1.0)
     */
    public function getOffsetY(): float
    {
        return $this->offsetY;
    }

    /**
     * Checks if aspect ratio should be maintained.
     *
     * @return bool True if aspect ratio should be maintained
     */
    public function shouldMaintainAspectRatio(): bool
    {
        return $this->maintainAspectRatio;
    }

    /**
     * Validates the configuration against the given error correction level.
     *
     * @param Ecc $errorCorrectionLevel The QR code error correction level
     * @throws LogoException If logo size exceeds maximum for the ECC level
     */
    public function validateForErrorCorrectionLevel(Ecc $errorCorrectionLevel): void
    {
        $maxSize = self::MAX_LOGO_SIZE_BY_ECC[$errorCorrectionLevel->name];
        
        if ($this->sizePercentage > $maxSize) {
            throw LogoException::sizeConstraintViolation(
                $this->sizePercentage,
                $maxSize,
                $errorCorrectionLevel->name
            );
        }
    }

    /**
     * Gets the SVG content from the logo source.
     *
     * @return string The SVG content
     * @throws LogoException If logo source cannot be loaded or is invalid
     */
    public function getLogoSvgContent(): string
    {
        // Check if it's already SVG content (starts with <svg)
        if (str_starts_with(trim($this->logoSource), '<svg')) {
            $this->validateSvgContent($this->logoSource);
            return $this->logoSource;
        }

        // Check if it's a URL
        if (filter_var($this->logoSource, FILTER_VALIDATE_URL)) {
            return $this->loadSvgFromUrl($this->logoSource);
        }

        // Treat as file path
        return $this->loadSvgFromFile($this->logoSource);
    }

    /**
     * Gets the maximum allowed logo size for an error correction level.
     *
     * @param Ecc $errorCorrectionLevel The error correction level
     * @return float Maximum logo size as percentage
     */
    public static function getMaxLogoSize(Ecc $errorCorrectionLevel): float
    {
        return self::MAX_LOGO_SIZE_BY_ECC[$errorCorrectionLevel->name];
    }

    /**
     * Validates the logo source parameter.
     *
     * @param string $logoSource The logo source to validate
     * @throws LogoException If logo source is invalid
     */
    private function validateLogoSource(string $logoSource): void
    {
        if (empty(trim($logoSource))) {
            throw LogoException::invalidConfiguration(
                'logoSource',
                $logoSource,
                'non-empty string'
            );
        }
    }

    /**
     * Validates the size percentage parameter.
     *
     * @param float $sizePercentage The size percentage to validate
     * @throws LogoException If size percentage is invalid
     */
    private function validateSizePercentage(float $sizePercentage): void
    {
        if ($sizePercentage < self::MIN_SIZE_PERCENTAGE || $sizePercentage > 0.30) {
            throw LogoException::invalidConfiguration(
                'sizePercentage',
                $sizePercentage,
                sprintf('float between %.2f and 0.30', self::MIN_SIZE_PERCENTAGE)
            );
        }
    }

    /**
     * Validates the background color parameter.
     *
     * @param string $backgroundColor The background color to validate
     * @throws LogoException If background color is invalid
     */
    private function validateBackgroundColor(string $backgroundColor): void
    {
        // Basic CSS color validation (hex colors, named colors, etc.)
        if (!preg_match('/^#[0-9A-Fa-f]{6}$|^#[0-9A-Fa-f]{3}$|^[a-zA-Z]+$/', $backgroundColor)) {
            throw LogoException::invalidConfiguration(
                'backgroundColor',
                $backgroundColor,
                'valid CSS color (hex or named color)'
            );
        }
    }

    /**
     * Validates the background padding parameter.
     *
     * @param int $backgroundPadding The background padding to validate
     * @throws LogoException If background padding is invalid
     */
    private function validateBackgroundPadding(int $backgroundPadding): void
    {
        if ($backgroundPadding < 0 || $backgroundPadding > 10) {
            throw LogoException::invalidConfiguration(
                'backgroundPadding',
                $backgroundPadding,
                'integer between 0 and 10'
            );
        }
    }

    /**
     * Validates an offset parameter.
     *
     * @param float $offset The offset to validate
     * @param string $paramName The parameter name for error messages
     * @throws LogoException If offset is invalid
     */
    private function validateOffset(float $offset, string $paramName): void
    {
        if ($offset < -1.0 || $offset > 1.0) {
            throw LogoException::invalidConfiguration(
                $paramName,
                $offset,
                'float between -1.0 and 1.0'
            );
        }
    }

    /**
     * Validates SVG content.
     *
     * @param string $svgContent The SVG content to validate
     * @throws LogoException If SVG content is invalid
     */
    private function validateSvgContent(string $svgContent): void
    {
        // Basic SVG validation
        if (!str_contains($svgContent, '<svg') || !str_contains($svgContent, '</svg>')) {
            throw LogoException::invalidSvg('Missing required SVG tags');
        }

        // Check for potentially dangerous content
        $dangerousPatterns = ['<script', 'javascript:', 'onload=', 'onerror='];
        foreach ($dangerousPatterns as $pattern) {
            if (stripos($svgContent, $pattern) !== false) {
                throw LogoException::invalidSvg('SVG contains potentially dangerous content');
            }
        }
    }

    /**
     * Loads SVG content from a file.
     *
     * @param string $filePath The file path to load from
     * @return string The SVG content
     * @throws LogoException If file cannot be loaded or is invalid
     */
    private function loadSvgFromFile(string $filePath): string
    {
        if (!file_exists($filePath)) {
            throw LogoException::fileSystemError('read', $filePath);
        }

        if (!is_readable($filePath)) {
            throw LogoException::fileSystemError('read (permission denied)', $filePath);
        }

        $content = file_get_contents($filePath);
        if ($content === false) {
            throw LogoException::fileSystemError('read content', $filePath);
        }

        $this->validateSvgContent($content);
        return $content;
    }

    /**
     * Loads SVG content from a URL.
     *
     * @param string $url The URL to load from
     * @return string The SVG content
     * @throws LogoException If URL cannot be loaded or is invalid
     */
    private function loadSvgFromUrl(string $url): string
    {
        $context = stream_context_create([
            'http' => [
                'timeout' => 10,
                'user_agent' => 'QrCodeGen-PHP/1.1.0',
            ],
        ]);

        $content = @file_get_contents($url, false, $context);
        if ($content === false) {
            throw LogoException::networkError($url);
        }

        $this->validateSvgContent($content);
        return $content;
    }
}
