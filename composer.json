{"name": "qrcodegen/qrcodegen-php", "description": "High-quality QR Code generator library for PHP with logo embedding support", "type": "library", "keywords": ["qr-code", "qr", "generator", "barcode", "logo", "svg", "php"], "license": "MIT", "authors": [{"name": "QR Code Generator PHP Port", "email": "<EMAIL>"}], "require": {"php": "^8.2", "ext-gd": "*"}, "require-dev": {"phpunit/phpunit": "^10.0", "friendsofphp/php-cs-fixer": "^3.0", "phpstan/phpstan": "^1.0", "phpstan/phpstan-phpunit": "^1.0", "phpstan/phpstan-strict-rules": "^1.0"}, "autoload": {"psr-4": {"QrCodeGen\\": "src/"}, "files": ["src/Core/Utilities/Functions.php"]}, "autoload-dev": {"psr-4": {"QrCodeGen\\Tests\\": "tests/"}}, "scripts": {"test": "phpunit", "test-coverage": "phpunit --coverage-html coverage/ --coverage-clover coverage.xml", "cs-fix": "php-cs-fixer fix", "cs-check": "php-cs-fixer fix --dry-run --diff", "stan": "phpstan analyse", "quality": ["@cs-check", "@stan", "@test"], "examples": "php tools/validate-examples.php"}, "config": {"sort-packages": true, "optimize-autoloader": true, "preferred-install": "dist"}, "minimum-stability": "stable", "prefer-stable": true, "archive": {"exclude": ["/tests", "/tools", "/examples", "/.github", "/coverage", "phpunit.xml", ".php-cs-fixer.dist.php", "phpstan.neon"]}}