<?php

declare(strict_types=1);

/*
 * QR Code generator library (PHP)
 * 
 * Copyright (c) Project Nayuki. (MIT License)
 * https://www.nayuki.io/page/qr-code-generator-library
 * 
 * Permission is hereby granted, free of charge, to any person obtaining a copy of
 * this software and associated documentation files (the "Software"), to deal in
 * the Software without restriction, including without limitation the rights to
 * use, copy, modify, merge, publish, distribute, sublicense, and/or sell copies of
 * the Software, and to permit persons to whom the Software is furnished to do so,
 * subject to the following conditions:
 * - The above copyright notice and this permission notice shall be included in
 *   all copies or substantial portions of the Software.
 * - The Software is provided "as is", without warranty of any kind, express or
 *   implied, including but not limited to the warranties of merchantability,
 *   fitness for a particular purpose and noninfringement. In no event shall the
 *   authors or copyright holders be liable for any claim, damages or other
 *   liability, whether in an action of contract, tort or otherwise, arising from,
 *   out of or in connection with the Software or the use or other dealings in the
 *   Software.
 */

namespace QrCodeGen\Extensions\Logo\Exceptions;

/**
 * Exception thrown when logo-related operations fail.
 * 
 * This exception is used for various logo-related errors including:
 * - Invalid logo source (file not found, invalid URL, malformed SVG)
 * - Logo size constraints violations
 * - Configuration validation failures
 * - SVG parsing and processing errors
 * 
 * @package QrCodeGen\Exceptions
 * <AUTHOR> Code Generator PHP Port
 * @since 1.1.0
 */
class LogoException extends \Exception
{
    /**
     * Error code for invalid logo source.
     */
    public const INVALID_SOURCE = 1001;
    
    /**
     * Error code for logo size constraint violations.
     */
    public const SIZE_CONSTRAINT_VIOLATION = 1002;
    
    /**
     * Error code for invalid SVG content.
     */
    public const INVALID_SVG = 1003;
    
    /**
     * Error code for configuration validation failures.
     */
    public const INVALID_CONFIGURATION = 1004;
    
    /**
     * Error code for file system related errors.
     */
    public const FILE_SYSTEM_ERROR = 1005;
    
    /**
     * Error code for network related errors (URL fetching).
     */
    public const NETWORK_ERROR = 1006;
    
    /**
     * Error code for SVG processing errors.
     */
    public const SVG_PROCESSING_ERROR = 1007;

    /**
     * Creates a new LogoException for invalid logo source.
     *
     * @param string $source The invalid logo source
     * @param \Throwable|null $previous Previous exception for chaining
     * @return self
     */
    public static function invalidSource(string $source, ?\Throwable $previous = null): self
    {
        return new self(
            "Invalid logo source: {$source}",
            self::INVALID_SOURCE,
            $previous
        );
    }

    /**
     * Creates a new LogoException for size constraint violations.
     *
     * @param float $requestedSize The requested logo size percentage
     * @param float $maxAllowed The maximum allowed size percentage
     * @param string $eccLevel The error correction level name
     * @return self
     */
    public static function sizeConstraintViolation(
        float $requestedSize,
        float $maxAllowed,
        string $eccLevel
    ): self {
        return new self(
            sprintf(
                "Logo size %.1f%% exceeds maximum allowed %.1f%% for error correction level %s",
                $requestedSize * 100,
                $maxAllowed * 100,
                $eccLevel
            ),
            self::SIZE_CONSTRAINT_VIOLATION
        );
    }

    /**
     * Creates a new LogoException for invalid SVG content.
     *
     * @param string $reason The reason why the SVG is invalid
     * @param \Throwable|null $previous Previous exception for chaining
     * @return self
     */
    public static function invalidSvg(string $reason, ?\Throwable $previous = null): self
    {
        return new self(
            "Invalid SVG content: {$reason}",
            self::INVALID_SVG,
            $previous
        );
    }

    /**
     * Creates a new LogoException for configuration validation failures.
     *
     * @param string $parameter The invalid configuration parameter
     * @param mixed $value The invalid value
     * @param string $expected Description of expected value
     * @return self
     */
    public static function invalidConfiguration(string $parameter, mixed $value, string $expected): self
    {
        $valueStr = is_scalar($value) ? (string)$value : gettype($value);
        return new self(
            "Invalid configuration for '{$parameter}': got '{$valueStr}', expected {$expected}",
            self::INVALID_CONFIGURATION
        );
    }

    /**
     * Creates a new LogoException for file system errors.
     *
     * @param string $operation The file operation that failed
     * @param string $path The file path involved
     * @param \Throwable|null $previous Previous exception for chaining
     * @return self
     */
    public static function fileSystemError(string $operation, string $path, ?\Throwable $previous = null): self
    {
        return new self(
            "File system error during {$operation}: {$path}",
            self::FILE_SYSTEM_ERROR,
            $previous
        );
    }

    /**
     * Creates a new LogoException for network errors.
     *
     * @param string $url The URL that failed to load
     * @param \Throwable|null $previous Previous exception for chaining
     * @return self
     */
    public static function networkError(string $url, ?\Throwable $previous = null): self
    {
        return new self(
            "Network error loading logo from URL: {$url}",
            self::NETWORK_ERROR,
            $previous
        );
    }

    /**
     * Creates a new LogoException for SVG processing errors.
     *
     * @param string $operation The SVG operation that failed
     * @param \Throwable|null $previous Previous exception for chaining
     * @return self
     */
    public static function svgProcessingError(string $operation, ?\Throwable $previous = null): self
    {
        return new self(
            "SVG processing error during {$operation}",
            self::SVG_PROCESSING_ERROR,
            $previous
        );
    }
}
