<?php

declare(strict_types=1);

/*
 * QR Code generator library (PHP)
 * 
 * Copyright (c) Project Nayuki. (MIT License)
 * https://www.nayuki.io/page/qr-code-generator-library
 * 
 * Permission is hereby granted, free of charge, to any person obtaining a copy of
 * this software and associated documentation files (the "Software"), to deal in
 * the Software without restriction, including without limitation the rights to
 * use, copy, modify, merge, publish, distribute, sublicense, and/or sell copies of
 * the Software, and to permit persons to whom the Software is furnished to do so,
 * subject to the following conditions:
 * - The above copyright notice and this permission notice shall be included in
 *   all copies or substantial portions of the Software.
 * - The Software is provided "as is", without warranty of any kind, express or
 *   implied, including but not limited to the warranties of merchantability,
 *   fitness for a particular purpose and noninfringement. In no event shall the
 *   authors or copyright holders be liable for any claim, damages or other
 *   liability, whether in an action of contract, tort or otherwise, arising from,
 *   out of or in connection with the Software or the use or other dealings in the
 *   Software.
 */

namespace QrCodeGen\Core\Utilities;

/**
 * Appends the given number of low-order bits of the given value to the given buffer.
 * Requires 0 <= len <= 31 and 0 <= val < 2^len.
 * 
 * @param int $val The value to append bits from
 * @param int $len The number of bits to append (0-31)
 * @param array<int> $bb The bit buffer to append to (modified by reference)
 * @throws \InvalidArgumentException If parameters are out of range
 */
function appendBits(int $val, int $len, array &$bb): void
{
    if ($len < 0 || $len > 31 || ($val >> $len) !== 0) {
        throw new \InvalidArgumentException("Value out of range");
    }

    for ($i = $len - 1; $i >= 0; $i--) {
        $bb[] = ($val >> $i) & 1;
    }
}

/**
 * Returns true if the i'th bit of x is set to 1.
 * 
 * @param int $x The value to check
 * @param int $i The bit position to check (0-based from right)
 * @return bool True if the bit is set
 */
function getBit(int $x, int $i): bool
{
    return (($x >> $i) & 1) !== 0;
}

/**
 * Throws an exception if the given condition is false.
 * 
 * @param bool $condition The condition to check
 * @throws \RuntimeException If condition is false
 */
function assertCondition(bool $condition): void
{
    if (!$condition) {
        throw new \RuntimeException("Assertion error");
    }
}
