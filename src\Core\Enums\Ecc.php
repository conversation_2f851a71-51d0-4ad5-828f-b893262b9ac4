<?php

declare(strict_types=1);

/*
 * QR Code generator library (PHP)
 * 
 * Copyright (c) Project Nayuki. (MIT License)
 * https://www.nayuki.io/page/qr-code-generator-library
 * 
 * Permission is hereby granted, free of charge, to any person obtaining a copy of
 * this software and associated documentation files (the "Software"), to deal in
 * the Software without restriction, including without limitation the rights to
 * use, copy, modify, merge, publish, distribute, sublicense, and/or sell copies of
 * the Software, and to permit persons to whom the Software is furnished to do so,
 * subject to the following conditions:
 * - The above copyright notice and this permission notice shall be included in
 *   all copies or substantial portions of the Software.
 * - The Software is provided "as is", without warranty of any kind, express or
 *   implied, including but not limited to the warranties of merchantability,
 *   fitness for a particular purpose and noninfringement. In no event shall the
 *   authors or copyright holders be liable for any claim, damages or other
 *   liability, whether in an action of contract, tort or otherwise, arising from,
 *   out of or in connection with the Software or the use or other dealings in the
 *   Software.
 */

namespace QrCodeGen\Core\Enums;

/**
 * The error correction level in a QR Code symbol.
 * 
 * This enum represents the four standard error correction levels defined in the QR Code specification.
 * Higher error correction levels can recover from more damage but require more space.
 */
enum Ecc: int
{
    /**
     * The QR Code can tolerate about 7% erroneous codewords
     */
    case LOW = 0;
    
    /**
     * The QR Code can tolerate about 15% erroneous codewords
     */
    case MEDIUM = 1;
    
    /**
     * The QR Code can tolerate about 25% erroneous codewords
     */
    case QUARTILE = 2;
    
    /**
     * The QR Code can tolerate about 30% erroneous codewords
     */
    case HIGH = 3;

    /**
     * Returns the ordinal value of this error correction level (0-3).
     * 
     * @return int The ordinal value
     */
    public function getOrdinal(): int
    {
        return $this->value;
    }

    /**
     * Returns the format bits for this error correction level.
     * Used internally for encoding format information.
     * 
     * @return int The format bits (0-3)
     */
    public function getFormatBits(): int
    {
        return match ($this) {
            self::LOW => 1,
            self::MEDIUM => 0,
            self::QUARTILE => 3,
            self::HIGH => 2,
        };
    }
}
