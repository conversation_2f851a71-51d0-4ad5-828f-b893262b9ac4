# QR Code Generator Library for PHP 8.2+

A high-quality, feature-complete QR Code generator library for PHP 8.2+. This is a faithful port of the excellent [TypeScript QR Code generator](https://www.nayuki.io/page/qr-code-generator-library) by Project Nayuki, maintaining API compatibility while leveraging modern PHP features.

## Features

### Core Features

-   **Complete QR Code Model 2 support**: All 40 versions (sizes 21×21 to 177×177)
-   **Logo embedding**: Add SVG logos to QR code centers while maintaining readability
-   **All 4 error correction levels**: LOW (~7%), MEDIUM (~15%), QUARTILE (~25%), HIGH (~30%)
-   **Multiple encoding modes**: Numeric, Alphanumeric, Byte, and ECI
-   **Automatic optimization**: Chooses the smallest version and optimal encoding mode
-   **Advanced mask selection**: Evaluates all 8 mask patterns and selects the best one
-   **Enhanced SVG rendering**: Multiple styles including paths, rectangles, dots, and gradients
-   **Multiple output formats**: SVG (native), PNG, and JPEG support
-   **Flexible API**: High-level, mid-level, and low-level interfaces

### Modern PHP Features

-   **PHP 8.2+ compatibility**: Uses modern PHP features like enums, readonly properties, and union types
-   **Strict typing**: Full type hints and return types throughout
-   **PSR-12 compliant**: Follows PHP coding standards
-   **Comprehensive documentation**: Complete PHPDoc for all public methods

### Quality Assurance

-   **Identical output**: Generates the same QR codes as the original TypeScript library
-   **Comprehensive tests**: >90% code coverage with extensive test suite
-   **Performance optimized**: Efficient algorithms with minimal memory usage
-   **Error handling**: Proper exception handling with descriptive messages

## Installation

### Using Composer (Recommended)

```bash
composer require qrcodegen/qrcodegen-php
```

### Manual Installation

1. Download the source code
2. Include the autoloader or manually require the files:

```php
// Core QR code functionality
require_once 'src/functions.php';
require_once 'src/Ecc.php';
require_once 'src/Mode.php';
require_once 'src/QrSegment.php';
require_once 'src/QrCode.php';

// Logo embedding functionality (optional)
require_once 'src/Exceptions/LogoException.php';
require_once 'src/LogoConfig.php';
require_once 'src/QrCodeLogo.php';
require_once 'src/SvgRenderer.php';
```

## Quick Start

```php
<?php

use QrCodeGen\QrCode;
use QrCodeGen\Ecc;

// Simple text encoding
$qr = QrCode::encodeText("Hello, world!", Ecc::MEDIUM);

// Get QR code properties
echo "Version: " . $qr->version . "\n";
echo "Size: " . $qr->size . "×" . $qr->size . "\n";
echo "Error correction: " . $qr->errorCorrectionLevel->name . "\n";

// Read modules (pixels)
for ($y = 0; $y < $qr->size; $y++) {
    for ($x = 0; $x < $qr->size; $x++) {
        $isDark = $qr->getModule($x, $y);
        echo $isDark ? "██" : "  ";
    }
    echo "\n";
}
```

## Logo Embedding Feature

Add SVG logos to your QR codes while maintaining readability:

```php
<?php

use QrCodeGen\QrCode;
use QrCodeGen\QrCodeLogo;
use QrCodeGen\LogoConfig;
use QrCodeGen\Ecc;

// 1. Create a QR code
$qr = QrCode::encodeText("https://example.com", Ecc::MEDIUM);

// 2. Define your logo (SVG string, file path, or URL)
$logoSvg = '<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100">
    <circle cx="50" cy="50" r="40" fill="#007bff"/>
    <text x="50" y="55" text-anchor="middle" fill="white" font-size="20">QR</text>
</svg>';

// 3. Create logo configuration
$logoConfig = new LogoConfig(
    logoSource: $logoSvg,
    sizePercentage: 0.15,  // 15% of QR code size
    addBackground: true,
    backgroundColor: '#FFFFFF'
);

// 4. Create QR code with logo
$qrWithLogo = new QrCodeLogo($qr, $logoConfig);

// 5. Generate output
$svg = $qrWithLogo->toSvg();
file_put_contents('qr-with-logo.svg', $svg);

// Also supports PNG and JPEG (requires GD extension)
$png = $qrWithLogo->toPng(scale: 10);
file_put_contents('qr-with-logo.png', $png);
```

### Logo Feature Highlights

-   **Smart sizing**: Automatic size limits based on error correction levels
-   **Multiple sources**: Support for SVG strings, file paths, and URLs
-   **Flexible positioning**: Center placement with optional offsets
-   **Background options**: Optional backgrounds with customizable colors and padding
-   **Security**: Automatic SVG validation and sanitization
-   **Quality preservation**: Maintains QR code readability

For detailed documentation, see [LOGO_FEATURE_GUIDE.md](LOGO_FEATURE_GUIDE.md).

## API Reference

### High-Level API

#### QrCode::encodeText()

Encodes a text string into a QR Code.

```php
public static function encodeText(string $text, Ecc $ecl): QrCode
```

**Parameters:**

-   `$text`: The text to encode
-   `$ecl`: Error correction level

**Example:**

```php
$qr = QrCode::encodeText("Hello, world!", Ecc::MEDIUM);
```

#### QrCode::encodeBinary()

Encodes binary data into a QR Code.

```php
public static function encodeBinary(array $data, Ecc $ecl): QrCode
```

**Parameters:**

-   `$data`: Array of bytes (0-255)
-   `$ecl`: Error correction level

**Example:**

```php
$data = [72, 101, 108, 108, 111]; // "Hello"
$qr = QrCode::encodeBinary($data, Ecc::HIGH);
```

### Mid-Level API

#### QrCode::encodeSegments()

Encodes a list of segments with advanced options.

```php
public static function encodeSegments(
    array $segs,
    Ecc $ecl,
    int $minVersion = 1,
    int $maxVersion = 40,
    int $mask = -1,
    bool $boostEcl = true
): QrCode
```

**Parameters:**

-   `$segs`: Array of QrSegment objects
-   `$ecl`: Error correction level
-   `$minVersion`: Minimum version number (1-40)
-   `$maxVersion`: Maximum version number (1-40)
-   `$mask`: Mask pattern (-1 for auto, 0-7 for manual)
-   `$boostEcl`: Whether to boost error correction if possible

**Example:**

```php
use QrCodeGen\QrSegment;

$segments = [
    QrSegment::makeNumeric("123"),
    QrSegment::makeAlphanumeric("ABC"),
    QrSegment::makeBytes([33, 34, 35])
];
$qr = QrCode::encodeSegments($segments, Ecc::HIGH, 1, 10, -1, true);
```

### Segment Creation

#### QrSegment::makeNumeric()

Creates a numeric segment (digits 0-9 only).

```php
public static function makeNumeric(string $digits): QrSegment
```

#### QrSegment::makeAlphanumeric()

Creates an alphanumeric segment (0-9, A-Z, space, $%\*+-./:).

```php
public static function makeAlphanumeric(string $text): QrSegment
```

#### QrSegment::makeBytes()

Creates a byte segment for arbitrary binary data.

```php
public static function makeBytes(array $data): QrSegment
```

#### QrSegment::makeSegments()

Automatically creates optimal segments for the given text.

```php
public static function makeSegments(string $text): array
```

## Error Correction Levels

```php
use QrCodeGen\Ecc;

Ecc::LOW      // ~7% error recovery
Ecc::MEDIUM   // ~15% error recovery
Ecc::QUARTILE // ~25% error recovery
Ecc::HIGH     // ~30% error recovery
```

## Examples

### Basic Usage

```php
// Encode different types of data
$qr1 = QrCode::encodeText("https://example.com", Ecc::MEDIUM);
$qr2 = QrCode::encodeText("123456789", Ecc::LOW); // Automatically uses numeric mode
$qr3 = QrCode::encodeText("HELLO WORLD", Ecc::HIGH); // Automatically uses alphanumeric mode
```

### Advanced Usage

```php
// Manual segment creation for optimal encoding
$segments = [
    QrSegment::makeAlphanumeric("PRODUCT-"),
    QrSegment::makeNumeric("12345"),
    QrSegment::makeBytes([0x20]), // Space character
    QrSegment::makeAlphanumeric("BATCH-"),
    QrSegment::makeNumeric("67890")
];

$qr = QrCode::encodeSegments(
    $segments,
    Ecc::MEDIUM,
    1,    // Min version
    10,   // Max version
    3,    // Force mask pattern 3
    false // Don't boost error correction
);
```

### Output to Different Formats

```php
// Generate SVG
function toSvg(QrCode $qr, int $border = 4): string
{
    $size = $qr->size + $border * 2;
    $svg = "<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 $size $size'>\n";
    $svg .= "<rect width='$size' height='$size' fill='white'/>\n";

    for ($y = 0; $y < $qr->size; $y++) {
        for ($x = 0; $x < $qr->size; $x++) {
            if ($qr->getModule($x, $y)) {
                $px = $x + $border;
                $py = $y + $border;
                $svg .= "<rect x='$px' y='$py' width='1' height='1' fill='black'/>\n";
            }
        }
    }
    $svg .= "</svg>";
    return $svg;
}

$qr = QrCode::encodeText("Hello, world!", Ecc::MEDIUM);
file_put_contents("qrcode.svg", toSvg($qr));
```

## Migration from TypeScript Version

This PHP library maintains API compatibility with the original TypeScript version:

| TypeScript                           | PHP                         |
| ------------------------------------ | --------------------------- |
| `qrcodegen.QrCode.encodeText()`      | `QrCode::encodeText()`      |
| `qrcodegen.QrSegment.makeSegments()` | `QrSegment::makeSegments()` |
| `qrcodegen.QrCode.Ecc.MEDIUM`        | `Ecc::MEDIUM`               |
| `qr.getModule(x, y)`                 | `$qr->getModule($x, $y)`    |

### Key Differences

1. **Namespaces**: Use `QrCodeGen\` namespace instead of `qrcodegen`
2. **Static methods**: Use `::` instead of `.` for static method calls
3. **Variables**: Use `$` prefix for variables
4. **Arrays**: Use PHP arrays instead of TypeScript arrays
5. **Enums**: Use PHP 8.1+ enums instead of classes

## Requirements

-   PHP 8.2 or higher
-   No external dependencies

## License

This project is licensed under the MIT License - see the original [TypeScript implementation](https://www.nayuki.io/page/qr-code-generator-library) for details.

## Contributing

1. Fork the repository
2. Create a feature branch
3. Add tests for new functionality
4. Ensure all tests pass
5. Submit a pull request

## Testing

```bash
# Run basic tests
php test_basic.php

# Run QR generation tests
php test_qr_generation.php

# Run with PHPUnit (if available)
vendor/bin/phpunit
```

## Performance

This library is optimized for both speed and memory usage:

-   **Memory efficient**: Minimal memory allocation during generation
-   **Fast encoding**: Optimized algorithms for all encoding modes
-   **Scalable**: Handles all QR code versions efficiently

## Support

For questions, issues, or contributions, please visit the [GitHub repository](https://github.com/qrcodegen/qrcodegen-php).

---

_This library is a faithful port of Project Nayuki's excellent QR Code generator. All credit for the original algorithms and design goes to the original author._
