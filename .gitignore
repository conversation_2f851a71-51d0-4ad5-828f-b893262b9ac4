# Composer
/vendor/
composer.lock

# PHPUnit
/.phpunit.cache/
/coverage/
coverage.xml
junit.xml

# PHPStan
/.phpstan.cache/
phpstan-baseline.neon

# PHP CS Fixer
/.php-cs-fixer.cache

# IDE files
/.vscode/
/.idea/
*.swp
*.swo
*~

# OS files
.DS_Store
Thumbs.db

# Generated examples
/examples/outputs/*.svg
/examples/outputs/*.png

# Test outputs
/tests/outputs/
test_*.svg
test_*.png

# Logs
*.log

# Temporary files
*.tmp
*.temp

# Node.js (if any build tools are added)
node_modules/
package-lock.json
yarn.lock

# Build artifacts
/build/
/dist/
