# Testing Guide

This guide covers testing procedures for the QR Code Generator library with logo embedding functionality.

## 🧭 Manual Testing Procedures

### Feature Testing Checklist

#### Core QR Code Generation
- [ ] Generate QR codes with different error correction levels (LOW, MEDIUM, QUARTILE, HIGH)
- [ ] Test different data types (numeric, alphanumeric, byte, mixed)
- [ ] Verify QR code versions 1-40 work correctly
- [ ] Test automatic mode selection and optimization
- [ ] Validate mask pattern selection

#### Logo Embedding
- [ ] Test logo embedding with different SVG sources (string, file, URL)
- [ ] Verify logo size constraints for each error correction level
- [ ] Test logo positioning (center, with offsets)
- [ ] Validate background options (enabled/disabled, colors, padding)
- [ ] Test complex logos with gradients and multiple elements

#### Output Formats
- [ ] SVG output validation (well-formed XML, correct structure)
- [ ] PNG output (if GD extension available)
- [ ] JPEG output (if GD extension available)
- [ ] Test different scales and border sizes

#### Error Handling
- [ ] Invalid logo sources (malformed SVG, missing files, network errors)
- [ ] Logo size constraint violations
- [ ] Invalid configuration parameters
- [ ] Missing dependencies (GD extension)

### Browser Compatibility Verification

Test generated SVG files in:
- [ ] Chrome (latest)
- [ ] Firefox (latest)
- [ ] Safari (latest)
- [ ] Edge (latest)
- [ ] Mobile browsers (iOS Safari, Chrome Mobile)

### Accessibility Testing Steps

- [ ] SVG includes proper title and description elements when specified
- [ ] High contrast between QR code and background
- [ ] Logo doesn't interfere with QR code readability
- [ ] Proper semantic markup in generated SVG

### Performance Validation

- [ ] Memory usage remains reasonable for large QR codes
- [ ] Generation time is acceptable for different QR code sizes
- [ ] Logo processing doesn't significantly impact performance

### User Workflow Validation

1. **Basic Workflow**:
   - Create QR code → Add logo → Generate output → Verify readability

2. **Advanced Workflow**:
   - Create QR code → Configure complex logo → Test different outputs → Validate across devices

## 🔬 Unit Testing Standards

### Coverage Goal
- **Target**: ≥90% line coverage
- **Minimum**: 85% line coverage for new features

### Test Structure
Use Describe-It pattern with AAA (Arrange-Act-Assert):

```php
// Arrange
$qr = QrCode::encodeText("Test", Ecc::MEDIUM);
$config = new LogoConfig($validSvg, 0.15);

// Act
$qrWithLogo = new QrCodeLogo($qr, $config);
$result = $qrWithLogo->toSvg();

// Assert
$this->assertStringContainsString('<svg', $result);
```

### Mocking Strategy
- Mock file system operations for logo loading tests
- Mock network requests for URL-based logo tests
- Use dependency injection where possible

### Test File Naming
- Unit tests: `*Test.php`
- Integration tests: `*IntegrationTest.php`
- Example: `LogoConfigTest.php`, `QrCodeLogoTest.php`

## 🧪 End-to-End Testing

### Framework
- **Primary**: Custom test runner (`run_tests_simple.php`)
- **Alternative**: PHPUnit (when available)

### Test Scenarios

#### Critical User Journeys
1. **Basic Logo Embedding**:
   ```
   Create QR → Define Logo → Configure → Generate SVG → Validate Output
   ```

2. **Multi-format Output**:
   ```
   Create QR → Add Logo → Generate SVG/PNG/JPEG → Compare Quality
   ```

3. **Error Recovery**:
   ```
   Invalid Input → Catch Exception → Display Error → Retry with Valid Input
   ```

### Environment Setup

#### Local Testing
```bash
# Run basic functionality tests
php test_logo_basic.php

# Run validation tests
php test_validation.php

# Run comprehensive test suite
php run_tests_simple.php

# Run examples
php examples/logo_examples.php
```

#### CI Testing (if applicable)
```bash
# Install dependencies
composer install

# Run PHPUnit tests
vendor/bin/phpunit

# Run custom tests
php run_tests_simple.php

# Check code coverage
vendor/bin/phpunit --coverage-html coverage/
```

### Data Management
- Use temporary files for file-based tests
- Clean up generated test files after execution
- Use predictable test data for consistent results

## 🧼 Code Quality Standards

### TypeScript Compatibility
- **Strict mode**: Enabled with `declare(strict_types=1)`
- **Type hints**: All parameters and return types specified
- **No `mixed` types**: Use specific types or union types

### Linting Standards
- **PSR-12**: PHP coding standards compliance
- **PHPStan**: Level max analysis (when available)
- **Custom rules**: No unused variables, proper error handling

### Formatting Standards
- **Indentation**: 4 spaces (no tabs)
- **Line length**: 120 characters maximum
- **Braces**: Opening brace on same line for methods/functions
- **Arrays**: Short array syntax `[]` preferred

### Pre-commit Standards
- All tests must pass
- Code must be properly formatted
- No syntax errors or warnings
- Documentation must be updated for new features

## 🛠 Execution Commands

### Basic Testing
```bash
# Test core functionality
php test_logo_basic.php

# Test validation logic
php test_validation.php

# Debug specific issues
php debug_svg_validation.php
```

### Comprehensive Testing
```bash
# Run all unit tests
php run_tests_simple.php

# Generate examples
php examples/logo_examples.php

# Test different scenarios
php test_validation.php
```

### Coverage Analysis (with PHPUnit)
```bash
# Generate coverage report
vendor/bin/phpunit --coverage-html coverage/

# View coverage in browser
open coverage/index.html
```

### Performance Testing
```bash
# Time QR code generation
time php test_logo_basic.php

# Memory usage analysis
php -d memory_limit=32M test_logo_basic.php
```

## 🔧 Troubleshooting

### Common Issues and Solutions

#### Test Failures

**Issue**: "GD extension not available"
```bash
# Solution: Install GD extension or skip raster tests
sudo apt-get install php-gd  # Ubuntu/Debian
brew install php-gd          # macOS
```

**Issue**: "File not found" errors
```bash
# Solution: Check file paths and permissions
ls -la src/
chmod +r src/*.php
```

**Issue**: "Memory limit exceeded"
```bash
# Solution: Increase memory limit
php -d memory_limit=128M test_script.php
```

#### SVG Validation Issues

**Issue**: "Invalid SVG content"
- Check SVG syntax and structure
- Ensure proper XML namespaces
- Validate against SVG specification

**Issue**: "Logo too large for error correction level"
- Use higher error correction level (MEDIUM → HIGH)
- Reduce logo size percentage
- Check LogoConfig::getMaxLogoSize() limits

#### Output Quality Issues

**Issue**: "QR code not readable"
- Increase error correction level
- Reduce logo size
- Ensure sufficient contrast
- Test with actual QR code scanners

**Issue**: "Poor image quality"
- Increase scale factor for raster outputs
- Use SVG format when possible
- Check JPEG quality settings

### Debug Mode

Enable debug output by setting environment variable:
```bash
export QR_DEBUG=1
php test_script.php
```

### Logging

For detailed debugging, add logging to test scripts:
```php
error_log("QR Code size: " . $qr->size);
error_log("Logo config: " . json_encode($config));
```

## Success Criteria

### Unit Tests
- [ ] All unit tests pass (100% pass rate)
- [ ] Code coverage ≥90%
- [ ] No memory leaks or excessive memory usage
- [ ] Performance within acceptable limits

### Integration Tests
- [ ] All integration scenarios work correctly
- [ ] Cross-browser compatibility verified
- [ ] Mobile device compatibility confirmed
- [ ] Accessibility requirements met

### Quality Gates
- [ ] PSR-12 compliance verified
- [ ] No static analysis warnings
- [ ] Documentation is complete and accurate
- [ ] Examples work as documented

### User Acceptance
- [ ] QR codes are readable by standard scanners
- [ ] Logo quality is acceptable across output formats
- [ ] Error messages are clear and helpful
- [ ] API is intuitive and well-documented

## Continuous Improvement

### Metrics to Track
- Test execution time
- Code coverage percentage
- Number of test failures
- Performance benchmarks

### Regular Reviews
- Monthly test suite review
- Quarterly performance analysis
- Annual compatibility testing
- Continuous documentation updates

### Feedback Integration
- User-reported issues
- Performance bottlenecks
- Feature requests
- Security considerations
