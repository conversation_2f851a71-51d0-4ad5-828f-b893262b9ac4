<?php

declare(strict_types=1);

/**
 * Comprehensive test to validate the migration is working correctly
 * Tests both old structure (backward compatibility) and new structure
 */

require_once __DIR__ . '/tests/bootstrap.php';

echo "Migration Validation Test\n";
echo "=========================\n\n";

$testsPassed = 0;
$testsFailed = 0;

function runTest(string $name, callable $test): void {
    global $testsPassed, $testsFailed;
    
    echo "Testing: $name\n";
    try {
        $result = $test();
        if ($result === true) {
            echo "✅ PASSED\n\n";
            $testsPassed++;
        } else {
            echo "❌ FAILED: $result\n\n";
            $testsFailed++;
        }
    } catch (Exception $e) {
        echo "❌ FAILED: " . $e->getMessage() . "\n\n";
        $testsFailed++;
    }
}

// Test 1: Old structure still works
runTest("Old Structure - Basic QR Generation", function() {
    $qr = \QrCodeGen\QrCode::encodeText("Hello Old Structure", \QrCodeGen\Ecc::MEDIUM);
    return $qr->version > 0 && $qr->size > 0;
});

// Test 2: New structure works
runTest("New Structure - Basic QR Generation", function() {
    $qr = \QrCodeGen\Core\QrCode::encodeText("Hello New Structure", \QrCodeGen\Core\Enums\Ecc::MEDIUM);
    return $qr->version > 0 && $qr->size > 0;
});

// Test 3: Old structure logo functionality
runTest("Old Structure - Logo Functionality", function() {
    $qr = \QrCodeGen\QrCode::encodeText("Hello Logo", \QrCodeGen\Ecc::HIGH);
    $logoSvg = '<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><circle cx="50" cy="50" r="40" fill="blue"/></svg>';
    $config = new \QrCodeGen\LogoConfig($logoSvg, 0.15);
    $qrWithLogo = new \QrCodeGen\QrCodeLogo($qr, $config);
    $svg = $qrWithLogo->toSvg();
    return strlen($svg) > 1000 && str_contains($svg, '<svg');
});

// Test 4: New structure logo functionality
runTest("New Structure - Logo Functionality", function() {
    $qr = \QrCodeGen\Core\QrCode::encodeText("Hello New Logo", \QrCodeGen\Core\Enums\Ecc::HIGH);
    $logoSvg = '<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><circle cx="50" cy="50" r="40" fill="red"/></svg>';
    $config = new \QrCodeGen\Extensions\Logo\LogoConfig($logoSvg, 0.15);
    $qrWithLogo = new \QrCodeGen\Extensions\Logo\QrCodeLogo($qr, $config);
    $svg = $qrWithLogo->toSvg();
    return strlen($svg) > 1000 && str_contains($svg, '<svg');
});

// Test 5: Utility functions work in both namespaces
runTest("Utility Functions - Both Namespaces", function() {
    // Old namespace
    $bb1 = [];
    \QrCodeGen\appendBits(5, 3, $bb1);
    $oldResult = implode('', $bb1) === '101';
    
    // New namespace
    $bb2 = [];
    \QrCodeGen\Core\Utilities\appendBits(5, 3, $bb2);
    $newResult = implode('', $bb2) === '101';
    
    return $oldResult && $newResult;
});

// Test 6: Enums work in both namespaces
runTest("Enums - Both Namespaces", function() {
    $oldEcc = \QrCodeGen\Ecc::MEDIUM;
    $newEcc = \QrCodeGen\Core\Enums\Ecc::MEDIUM;
    
    $oldMode = \QrCodeGen\Mode::BYTE;
    $newMode = \QrCodeGen\Core\Enums\Mode::BYTE;
    
    return $oldEcc->getOrdinal() === $newEcc->getOrdinal() && 
           $oldMode->getModeBits() === $newMode->getModeBits();
});

// Test 7: Cross-namespace compatibility
runTest("Cross-Namespace Compatibility", function() {
    // Create QR with old namespace
    $qr = \QrCodeGen\QrCode::encodeText("Cross Test", \QrCodeGen\Ecc::HIGH);
    
    // Use with new namespace logo
    $logoSvg = '<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><rect x="25" y="25" width="50" height="50" fill="green"/></svg>';
    $config = new \QrCodeGen\Extensions\Logo\LogoConfig($logoSvg, 0.20);
    
    // This should work because of backward compatibility
    $qrWithLogo = new \QrCodeGen\QrCodeLogo($qr, $config);
    $svg = $qrWithLogo->toSvg();
    
    return strlen($svg) > 1000;
});

// Test 8: Autoloading works correctly
runTest("Autoloading - All Classes Load", function() {
    $classes = [
        \QrCodeGen\QrCode::class,
        \QrCodeGen\QrSegment::class,
        \QrCodeGen\Ecc::class,
        \QrCodeGen\Mode::class,
        \QrCodeGen\Core\QrCode::class,
        \QrCodeGen\Core\QrSegment::class,
        \QrCodeGen\Core\Enums\Ecc::class,
        \QrCodeGen\Core\Enums\Mode::class,
        \QrCodeGen\Extensions\Logo\LogoConfig::class,
        \QrCodeGen\Extensions\Logo\Exceptions\LogoException::class,
    ];
    
    foreach ($classes as $class) {
        if (!class_exists($class) && !enum_exists($class)) {
            return "Class/Enum not found: $class";
        }
    }
    
    return true;
});

// Summary
echo "Migration Validation Summary\n";
echo "============================\n";
echo "Tests Passed: $testsPassed\n";
echo "Tests Failed: $testsFailed\n";

if ($testsFailed === 0) {
    echo "\n🎉 All migration tests passed!\n";
    echo "✅ Backward compatibility maintained\n";
    echo "✅ New structure working correctly\n";
    echo "✅ Cross-namespace compatibility verified\n";
    echo "✅ Autoloading functioning properly\n";
} else {
    echo "\n❌ Some tests failed. Migration needs attention.\n";
    exit(1);
}
