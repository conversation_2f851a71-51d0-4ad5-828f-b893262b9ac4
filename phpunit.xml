<?xml version="1.0" encoding="UTF-8"?>
<phpunit xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:noNamespaceSchemaLocation="https://schema.phpunit.de/10.0/phpunit.xsd"
         bootstrap="tests/bootstrap.php"
         cacheDirectory=".phpunit.cache"
         executionOrder="depends,defects"
         requireCoverageMetadata="true"
         beStrictAboutCoverageMetadata="true"
         beStrictAboutOutputDuringTests="true"
         failOnRisky="true"
         failOnWarning="true">
    
    <testsuites>
        <testsuite name="Unit">
            <directory>tests/Unit</directory>
        </testsuite>
        <testsuite name="Integration">
            <directory>tests/Integration</directory>
        </testsuite>
        <testsuite name="Core">
            <directory>tests/Unit/Core</directory>
        </testsuite>
        <testsuite name="Extensions">
            <directory>tests/Unit/Extensions</directory>
        </testsuite>
    </testsuites>

    <source>
        <include>
            <directory>src</directory>
        </include>
        <exclude>
            <directory>src/Contracts</directory>
        </exclude>
    </source>

    <coverage>
        <report>
            <html outputDirectory="coverage/html"/>
            <clover outputFile="coverage/clover.xml"/>
            <text outputFile="coverage/coverage.txt"/>
        </report>
        <thresholds>
            <coverage>
                <line>90</line>
                <method>90</method>
                <class>90</class>
            </coverage>
        </thresholds>
    </coverage>

    <logging>
        <junit outputFile="coverage/junit.xml"/>
    </logging>

    <php>
        <ini name="error_reporting" value="-1"/>
        <ini name="memory_limit" value="-1"/>
        <env name="APP_ENV" value="testing"/>
    </php>
</phpunit>
