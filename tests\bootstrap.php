<?php

declare(strict_types=1);

/**
 * PHPUnit Bootstrap File
 * 
 * This file is loaded before running tests and sets up the testing environment.
 */

// Load Composer autoloader if available
if (file_exists(__DIR__ . '/../vendor/autoload.php')) {
    require_once __DIR__ . '/../vendor/autoload.php';
} else {
    // Fallback to manual autoloading for development
    spl_autoload_register(function ($class) {
        $prefix = 'QrCodeGen\\';
        $baseDir = __DIR__ . '/../src/';
        
        $len = strlen($prefix);
        if (strncmp($prefix, $class, $len) !== 0) {
            return;
        }
        
        $relativeClass = substr($class, $len);
        $file = $baseDir . str_replace('\\', '/', $relativeClass) . '.php';
        
        if (file_exists($file)) {
            require $file;
        }
    });
    
    // Load utility functions - load both for compatibility
    if (file_exists(__DIR__ . '/../src/Core/Utilities/Functions.php')) {
        require_once __DIR__ . '/../src/Core/Utilities/Functions.php';
    }
    if (file_exists(__DIR__ . '/../src/functions.php')) {
        require_once __DIR__ . '/../src/functions.php';
    }
}

// Set error reporting for tests
error_reporting(E_ALL);
ini_set('display_errors', '1');

// Set timezone for consistent test results
date_default_timezone_set('UTC');

// Define test constants
define('QRCODEGEN_TEST_FIXTURES_DIR', __DIR__ . '/Fixtures');
define('QRCODEGEN_TEST_OUTPUTS_DIR', __DIR__ . '/../examples/outputs');

// Ensure output directory exists
if (!is_dir(QRCODEGEN_TEST_OUTPUTS_DIR)) {
    mkdir(QRCODEGEN_TEST_OUTPUTS_DIR, 0755, true);
}
